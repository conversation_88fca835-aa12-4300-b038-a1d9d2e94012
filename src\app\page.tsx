import { Metadata } from 'next';
import Image from "next/image";
import { Header } from '@/components/Header';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

const placeholderImg = "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=80&q=80";
const mapPlaceholder = "https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=200&q=80";
const weatherPlaceholder = "https://images.unsplash.com/photo-1501594907352-04cda38ebc29?auto=format&fit=crop&w=48&q=80";

export const metadata: Metadata = {
  title: 'Home',
  description: 'Welcome to our Next.js application',
};

export default function Home() {
  return (
    <main className="min-h-screen bg-gray-100 relative overflow-hidden font-sans">
      {/* Fondo difuminado */}
      <div className="absolute inset-0 -z-10">
        <Image src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=1200&q=80" alt="Fondo campo" fill className="object-cover blur-sm" />
        <div className="absolute inset-0 bg-black bg-opacity-30" />
      </div>
      <Header />
      <div className="max-w-6xl mx-auto p-8 grid gap-8">
        {/* Top Section */}
        <div className="grid grid-cols-5 gap-2">
          {/* Left Field Card */}
          <div className="col-span-1 bg-white rounded-2xl shadow-lg p-4 flex flex-col border border-gray-100">
            <div className="w-full h-24 mb-1 relative overflow-hidden rounded-xl">
              {/* Imagen aérea de stock */}
              <Image src="https://images.unsplash.com/photo-1501785888041-af3ef285b470?auto=format&fit=crop&w=400&q=80" alt="Corn field" fill className="object-cover" />
              {/* Polígono SVG */}
              <svg className="absolute inset-0 w-full h-full" viewBox="0 0 112 80" fill="none">
                <polygon points="22,58 38,28 85,22 95,48 68,68" fill="#D9F99D" fillOpacity="0.5" stroke="#A3E635" strokeWidth="2" />
              </svg>
            </div>
            <div className="flex items-center justify-between w-full mt-1 gap-2">
              <div className="flex flex-col">
                <div className="text-base font-bold leading-tight">Corn field</div>
                <div className="text-xs text-gray-400 leading-tight">18 ha</div>
              </div>
              <button className="bg-lime-300 hover:bg-lime-400 text-green-900 rounded-full w-8 h-8 flex items-center justify-center shadow transition-all duration-200">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                  <path d="M8 12H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                  <path d="M13 9L16 12L13 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </button>
            </div>
          </div>
          {/* Weather Card */}
          <div className="col-span-1 bg-white rounded-2xl shadow-lg p-4 flex flex-col justify-between border border-gray-100">
            <div className="text-gray-700 font-medium mb-2">Vynnyky, Lviv Oblast, Ukraine</div>
            <div className="flex items-center gap-4 mb-2">
              <div className="text-5xl font-extrabold">+16°</div>
              <div className="flex flex-col text-xs text-gray-500">
                <span>H: +19°</span>
                <span>L: +10°</span>
              </div>
              <div className="ml-auto">
                <Image src={weatherPlaceholder} alt="Cloudy" width={48} height={48} />
              </div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-2">
              <span>Humidity <b className="text-gray-700">74%</b></span>
              <span>Precipitation <b className="text-gray-700">5 mm</b></span>
              <span>Pressure <b className="text-gray-700">1019 hPa</b></span>
              <span>Wind <b className="text-gray-700">18 km/h</b></span>
            </div>
          </div>
          {/* Rate Card */}
          <div className="col-span-2 bg-white rounded-2xl shadow-lg py-0.5 px-1 flex flex-col gap-0 border border-gray-200">
            <div className="flex items-center gap-1 mb-0">
              <span className="text-[0.4rem] text-gray-500 leading-none">Standard rate</span>
              <input className="border rounded px-0.5 py-0 w-12 text-[0.4rem] leading-none" defaultValue="100" readOnly />
              <div className="flex bg-gray-200 rounded-full px-0.5 py-0 gap-0.5">
                <button className="bg-lime-300 text-green-900 rounded-full px-1 py-0.5 text-[0.35rem] font-semibold shadow leading-none">kg/ha</button>
                <button className="text-gray-500 rounded-full px-1 py-0.5 text-[0.35rem] font-semibold leading-none">L/ha</button>
              </div>
            </div>
            <div className="flex justify-between items-center text-[0.4rem] text-gray-500 mb-0 leading-none">
              <span>Rate for zone</span>
              <button className="text-lime-600 hover:text-lime-700 font-medium leading-none">Edit</button>
            </div>
            <div className="grid grid-cols-2 gap-[1px]">
              <div className="flex flex-col items-start bg-gray-100 rounded-lg py-[0.5px] px-[1px]">
                <div className="flex items-center gap-0.5 text-[0.45rem] mb-[0px] leading-none">
                  <span className="w-1.5 h-1.5 rounded-full bg-purple-500 inline-block"></span>
                  <span className="font-semibold text-gray-700 text-[0.45rem] leading-none">Very high</span>
                </div>
                <div className="grid grid-cols-2 gap-0.5 leading-none">
                  <span className="text-sm font-extrabold text-gray-800 whitespace-nowrap leading-none">70 <span className="text-[0.4rem] text-gray-500 leading-none">kg/h</span></span>
                  <span className="text-[0.4rem] text-gray-500 whitespace-nowrap text-right leading-none">1,8 ha</span>
                </div>
              </div>
              <div className="flex flex-col items-start bg-gray-100 rounded-lg py-[0.5px] px-[1px]">
                <div className="flex items-center gap-0.5 text-[0.45rem] mb-[0px] leading-none">
                  <span className="w-1.5 h-1.5 rounded-full bg-green-500 inline-block"></span>
                  <span className="font-semibold text-gray-700 text-[0.45rem] leading-none">High</span>
                </div>
                <div className="grid grid-cols-2 gap-0.5 leading-none">
                  <span className="text-sm font-extrabold text-gray-800 whitespace-nowrap leading-none">85 <span className="text-[0.4rem] text-gray-500 leading-none">kg/h</span></span>
                  <span className="text-[0.4rem] text-gray-500 whitespace-nowrap text-right leading-none">4,5 ha</span>
                </div>
              </div>
              <div className="flex flex-col items-start bg-gray-100 rounded-lg py-[0.5px] px-[1px]">
                <div className="flex items-center gap-0.5 text-[0.45rem] mb-[0px] leading-none">
                  <span className="w-1.5 h-1.5 rounded-full bg-yellow-500 inline-block"></span>
                  <span className="font-semibold text-gray-700 text-[0.45rem] leading-none">Average</span>
                </div>
                <div className="grid grid-cols-2 gap-0.5 leading-none">
                  <span className="text-sm font-extrabold text-gray-800 whitespace-nowrap leading-none">100 <span className="text-[0.4rem] text-gray-500 leading-none">kg/h</span></span>
                  <span className="text-[0.4rem] text-gray-500 whitespace-nowrap text-right leading-none">5,5 ha</span>
                </div>
              </div>
              <div className="flex flex-col items-start bg-gray-100 rounded-lg py-[0.5px] px-[1px]">
                <div className="flex items-center gap-0.5 text-[0.45rem] mb-[0px] leading-none">
                  <span className="w-1.5 h-1.5 rounded-full bg-gray-400 inline-block"></span>
                  <span className="font-semibold text-gray-700 text-[0.45rem] leading-none">Low</span>
                </div>
                <div className="grid grid-cols-2 gap-0.5 leading-none">
                  <span className="text-sm font-extrabold text-gray-800 whitespace-nowrap leading-none">115 <span className="text-[0.4rem] text-gray-500 leading-none">kg/h</span></span>
                  <span className="text-[0.4rem] text-gray-500 whitespace-nowrap text-right leading-none">6,2 ha</span>
                </div>
              </div>
            </div>
          </div>
          {/* Right Field Card - Empty field */}
          <div className="col-span-1 bg-white rounded-2xl shadow-lg p-4 flex flex-col border border-gray-100">
            <div className="w-full h-24 mb-1 relative overflow-hidden rounded-xl">
              <Image src={mapPlaceholder} alt="Empty field" fill className="object-cover" />
              <svg className="absolute inset-0 w-full h-full" viewBox="0 0 112 80" fill="none">
                <polygon points="22,58 38,28 85,22 95,48 68,68" fill="#D9F99D" fillOpacity="0.5" stroke="#A3E635" strokeWidth="2" />
              </svg>
            </div>
            <div className="flex items-center justify-between w-full mt-1 gap-2">
              <div className="flex flex-col">
                <div className="text-base font-bold leading-tight">Empty field</div>
                <div className="text-xs text-gray-400 leading-tight">15 ha</div>
              </div>
              <button className="bg-lime-300 hover:bg-lime-400 text-green-900 rounded-full w-8 h-8 flex items-center justify-center shadow transition-all duration-200">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                  <path d="M8 12H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                  <path d="M13 9L16 12L13 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
        {/* Main Section */}
        <div className="grid grid-cols-5 gap-6 items-start">
          {/* Status Cards Block con fondo de imagen y blur */}
          <div className="col-span-3 relative flex flex-col items-center min-h-[260px]">
            {/* Imagen de fondo desenfocada */}
            <Image
              src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80"
              alt="Campo"
              fill
              className="object-cover blur-md absolute inset-0 z-0 rounded-3xl"
              style={{objectPosition: 'center'}}
            />
            {/* Capa oscura translúcida sobre la imagen */}
            <div className="absolute inset-0 bg-black bg-opacity-40 z-10 rounded-3xl" />
            {/* Cards */}
            <div className="relative z-20 flex w-full gap-4 p-8">
              {/* Plant's health Card */}
              <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-6 flex flex-col items-center">
                <span className="mb-1 block">
                  {/* Icono Plant's health (contorno de hojas, verde) */}
                  <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#ph-blur)">
                      <ellipse cx="18" cy="28" rx="8" ry="3" fill="#22C55E" fillOpacity="0.15"/>
                    </g>
                    <path d="M18 28C18 28 10 22 10 15C10 10 14 6 18 6C22 6 26 10 26 15C26 22 18 28 18 28Z" stroke="#22C55E" strokeWidth="2" fill="none"/>
                    <path d="M18 28C18 28 14 22 14 17C14 14 16 12 18 12C20 12 22 14 22 17C22 22 18 28 18 28Z" stroke="#22C55E" strokeWidth="2" fill="none"/>
                    <defs>
                      <filter id="ph-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                        <feGaussianBlur stdDeviation="1.5"/>
                      </filter>
                    </defs>
                  </svg>
                </span>
                <span className="text-4xl font-extrabold text-white drop-shadow">93%</span>
                <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
                <div className="text-white font-semibold text-lg drop-shadow">Plant's health</div>
              </div>
              {/* Water depth Card */}
              <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-6 flex flex-col items-center">
                <span className="mb-1 block">
                  {/* Icono Water depth (gota de agua, azul) */}
                  <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#wd-blur)">
                      <ellipse cx="18" cy="28" rx="8" ry="3" fill="#38BDF8" fillOpacity="0.15"/>
                    </g>
                    <path d="M18 7C18 7 26 17 26 23C26 27 22.4183 31 18 31C13.5817 31 10 27 10 23C10 17 18 7 18 7Z" stroke="#BAE6FD" strokeWidth="2" fill="#38BDF8"/>
                    <defs>
                      <filter id="wd-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                        <feGaussianBlur stdDeviation="1.5"/>
                      </filter>
                    </defs>
                  </svg>
                </span>
                <span className="text-4xl font-extrabold text-white drop-shadow">85%</span>
                <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
                <div className="text-white font-semibold text-lg drop-shadow">Water depth</div>
              </div>
              {/* Soil Card */}
              <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-6 flex flex-col items-center">
                <span className="mb-1 block">
                  {/* Icono Soil (globo terráqueo, amarillo/verde) */}
                  <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#soil-blur)">
                      <ellipse cx="18" cy="28" rx="8" ry="3" fill="#FACC15" fillOpacity="0.15"/>
                    </g>
                    <circle cx="18" cy="17" r="7" stroke="#FACC15" strokeWidth="2" fill="#FDE68A"/>
                    <path d="M11 17C11 17 13.5 14 18 14C22.5 14 25 17 25 17" stroke="#22C55E" strokeWidth="1.5" fill="none"/>
                    <path d="M18 10C18 10 18 24 18 24" stroke="#22C55E" strokeWidth="1.5" fill="none"/>
                    <defs>
                      <filter id="soil-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                        <feGaussianBlur stdDeviation="1.5"/>
                      </filter>
                    </defs>
                  </svg>
                </span>
                <span className="text-4xl font-extrabold text-white drop-shadow">74%</span>
                <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
                <div className="text-white font-semibold text-lg drop-shadow">Soil</div>
              </div>
            </div>
            <div className="relative z-20 flex justify-between w-full text-white text-base drop-shadow mt-2 px-8">
              <span>10 days to harvest</span>
              <span>64/74</span>
            </div>
            {/* Barra de progreso del cultivo */}
            <div className="relative z-20 w-full flex items-center justify-center px-8 mt-2">
              <div className="w-full h-3 bg-white/20 rounded-full overflow-hidden shadow-inner">
                <div
                  className="h-full bg-green-400 rounded-full transition-all duration-500"
                  style={{ width: `${(64/74)*100}%` }}
                />
              </div>
            </div>
            {/* Espacio extra para centrar y permitir barra de progreso */}
            <div className="h-8" />
          </div>
          {/* Try for Free Vertical Button */}
          <div className="col-span-1 flex flex-col items-center justify-center">
            <button className="bg-lime-400 hover:bg-lime-500 text-green-900 font-bold rounded-full px-8 py-16 shadow-2xl tracking-widest text-lg flex flex-col items-center justify-center transition-all duration-200" style={{ writingMode: 'vertical-rl', transform: 'rotate(180deg)' }}>
              <span className="mb-2">TRY FOR FREE</span>
              <span>
                {/* Flecha SVG */}
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="16" cy="16" r="16" fill="#22C55E"/>
                  <path d="M16 10V22" stroke="#fff" strokeWidth="2" strokeLinecap="round"/>
                  <path d="M10 16L16 10L22 16" stroke="#fff" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </span>
            </button>
          </div>
          {/* Map Card */}
          <div className="col-span-1 bg-white rounded-2xl shadow-lg p-6 flex flex-col gap-2">
            <div className="relative w-full h-24 mb-2">
              <Image src={mapPlaceholder} alt="Map" fill className="rounded-lg object-cover" />
              <div className="absolute top-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded font-bold shadow">High<br/>85 kg/h<br/>4.5 ha</div>
            </div>
            <div className="text-xs text-gray-500">Low <span className="bg-gray-200 text-gray-700 rounded px-1 font-bold">115 kg/h, 6.2 ha</span></div>
            <div className="text-xs text-gray-500">Phosphorus <b className="text-gray-700">10.5</b></div>
            <div className="text-xs text-gray-500">Magnesium <b className="text-gray-700">7.2</b></div>
            <div className="text-xs text-gray-500">Acidity <b className="text-gray-700">3.0</b></div>
            <div className="text-xs text-gray-500">Humidity <b className="text-gray-700">38%</b></div>
            <button className="bg-green-200 text-green-700 rounded-full px-4 py-2 flex items-center gap-1 self-end mt-2 font-medium shadow">
              <ArrowForwardIcon fontSize="small" />
            </button>
          </div>
        </div>
        {/* Bottom Section */}
        <div className="grid grid-cols-5 gap-6 items-center">
          {/* Crop Selector and Standard Rate */}
          <div className="col-span-3 bg-white rounded-2xl shadow-lg p-6 flex items-center gap-4 border border-gray-100">
            <select className="border rounded px-2 py-1 text-base">
              <option>Corn, grain</option>
              <option>Wheat</option>
              <option>Barley</option>
            </select>
            <span className="text-sm text-gray-500">Standard rate</span>
            <input className="border rounded px-2 py-1 w-16 text-sm" defaultValue="100" readOnly />
            <span className="text-sm bg-green-200 text-green-700 rounded px-2 py-0.5 font-semibold">kg/ha</span>
            <span className="text-sm text-gray-500">Productivity zones</span>
            <div className="flex gap-1">
              {[3,4,6,7].map((zone) => (
                <span key={zone} className="bg-green-200 text-green-900 rounded px-2 py-0.5 text-sm font-bold">{zone}</span>
              ))}
            </div>
          </div>
          {/* Growth Rate Chart */}
          <div className="col-span-2 bg-white rounded-2xl shadow-lg p-6 flex flex-col items-center border border-gray-100">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-sm text-gray-500">Growth rate</span>
              <span className="text-xl font-extrabold">0.75</span>
              <span className="text-green-500 text-lg">↓</span>
            </div>
            {/* Placeholder for chart */}
            <div className="w-full h-20 bg-green-100 rounded flex items-end gap-1 px-2">
              {/* Simulated bar chart */}
              {[4,8,6,10,7,12,8,6,9,5,7,8].map((v,i) => (
                <div key={i} className="bg-green-400 rounded w-3" style={{height: `${v*7}px`}}></div>
              ))}
            </div>
            <div className="flex justify-between w-full text-xs text-gray-400 mt-1 px-2">
              <span>W</span>
              <span>M</span>
              <span>Y</span>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
